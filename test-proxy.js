#!/usr/bin/env node

/**
 * Z2API 代理测试脚本
 * 用于测试代理配置是否正常工作
 */

const http = require('http');
const https = require('https');

// 测试代理配置
function parseProxyConfig() {
    const proxyConfig = {
        enabled: false,
        host: null,
        port: null,
        auth: null,
        rejectUnauthorized: true
    };

    const proxyUrl = process.env.HTTP_PROXY ||
                    process.env.http_proxy ||
                    process.env.HTTPS_PROXY ||
                    process.env.https_proxy;

    if (proxyUrl) {
        try {
            const parsed = new URL(proxyUrl);
            proxyConfig.enabled = true;
            proxyConfig.host = parsed.hostname;
            proxyConfig.port = parseInt(parsed.port) || (parsed.protocol === 'https:' ? 443 : 80);

            if (parsed.username && parsed.password) {
                proxyConfig.auth = `${parsed.username}:${parsed.password}`;
            }

            const disableTLS = process.env.DISABLE_TLS_VERIFY || process.env.NODE_TLS_REJECT_UNAUTHORIZED;
            if (disableTLS && ['false', '0', 'no'].includes(disableTLS.toLowerCase())) {
                proxyConfig.rejectUnauthorized = false;
            }

            console.log(`✅ 代理配置: ${proxyConfig.host}:${proxyConfig.port}`);
            if (proxyConfig.auth) {
                console.log(`   认证: 已启用`);
            }
            if (!proxyConfig.rejectUnauthorized) {
                console.log(`   TLS验证: 已禁用`);
            }
        } catch (error) {
            console.log(`❌ 无效的代理URL: ${proxyUrl}`);
            return proxyConfig;
        }
    } else {
        console.log(`ℹ️  未配置代理，将使用直连`);
    }

    return proxyConfig;
}

// 测试连接函数
function testConnection(options, proxyConfig) {
    return new Promise((resolve, reject) => {
        let requestOptions = { ...options };
        let requestModule = https;

        if (proxyConfig.enabled) {
            console.log(`🔄 通过代理连接: ${proxyConfig.host}:${proxyConfig.port}`);
            requestModule = http;
            requestOptions = {
                hostname: proxyConfig.host,
                port: proxyConfig.port,
                path: `https://${options.hostname}:${options.port || 443}${options.path}`,
                method: 'CONNECT',
                headers: {
                    'Host': `${options.hostname}:${options.port || 443}`,
                    'User-Agent': 'Z2API-Test/1.0',
                    'Proxy-Connection': 'keep-alive'
                }
            };

            if (proxyConfig.auth) {
                requestOptions.headers['Proxy-Authorization'] = `Basic ${Buffer.from(proxyConfig.auth).toString('base64')}`;
            }

            const connectReq = requestModule.request(requestOptions);

            connectReq.on('socket', (socket) => {
                socket.setKeepAlive(true, 0);
                socket.setNoDelay(true);
                
                socket.on('error', (error) => {
                    reject(new Error(`Socket错误: ${error.message}`));
                });

                socket.on('close', (hadError) => {
                    if (hadError) {
                        reject(new Error('Socket在连接建立前关闭'));
                    }
                });
            });

            connectReq.on('connect', (res, socket, _head) => {
                if (res.statusCode === 200) {
                    console.log(`✅ 代理隧道建立成功`);
                    
                    socket.setKeepAlive(true, 0);
                    socket.setNoDelay(true);

                    const httpsOptions = {
                        ...options,
                        socket: socket,
                        rejectUnauthorized: proxyConfig.rejectUnauthorized,
                        servername: options.hostname
                    };

                    const req = https.request(httpsOptions, (res) => {
                        console.log(`✅ HTTPS请求成功，状态码: ${res.statusCode}`);
                        resolve({
                            status: res.statusCode,
                            statusText: res.statusMessage,
                            headers: res.headers
                        });
                    });

                    req.on('error', (error) => {
                        reject(new Error(`HTTPS请求错误: ${error.message}`));
                    });

                    socket.on('error', (error) => {
                        reject(new Error(`隧道Socket错误: ${error.message}`));
                    });

                    req.end();
                } else {
                    reject(new Error(`代理连接失败: ${res.statusCode} ${res.statusMessage}`));
                }
            });

            connectReq.on('error', (error) => {
                reject(new Error(`代理连接错误: ${error.message}`));
            });

            connectReq.on('timeout', () => {
                connectReq.destroy();
                reject(new Error('代理连接超时'));
            });

            connectReq.setTimeout(30000);
            connectReq.end();
        } else {
            console.log(`🔄 直接连接: ${options.hostname}`);
            if (!proxyConfig.rejectUnauthorized) {
                requestOptions.rejectUnauthorized = false;
            }

            const req = requestModule.request(requestOptions, (res) => {
                console.log(`✅ 直接请求成功，状态码: ${res.statusCode}`);
                resolve({
                    status: res.statusCode,
                    statusText: res.statusMessage,
                    headers: res.headers
                });
            });

            req.on('error', (error) => {
                reject(new Error(`直接请求错误: ${error.message}`));
            });

            req.on('timeout', () => {
                req.destroy();
                reject(new Error('直接请求超时'));
            });

            req.setTimeout(30000);
            req.end();
        }
    });
}

// 主测试函数
async function runTest() {
    console.log('🚀 开始代理连接测试...\n');

    const proxyConfig = parseProxyConfig();
    
    // 测试目标：Z.AI API
    const testOptions = {
        hostname: 'chat.z.ai',
        port: 443,
        path: '/api/chat/completions',
        method: 'HEAD',
        headers: {
            'User-Agent': 'Z2API-Test/1.0'
        }
    };

    try {
        console.log(`\n🎯 测试目标: ${testOptions.hostname}${testOptions.path}`);
        const result = await testConnection(testOptions, proxyConfig);
        console.log(`\n✅ 测试成功！`);
        console.log(`   状态码: ${result.status}`);
        console.log(`   状态信息: ${result.statusText}`);
        console.log(`   服务器: ${result.headers.server || '未知'}`);
        
        if (proxyConfig.enabled) {
            console.log(`\n🎉 代理配置工作正常！`);
        } else {
            console.log(`\n🎉 直连工作正常！`);
        }
    } catch (error) {
        console.error(`\n❌ 测试失败: ${error.message}`);
        console.error(`\n💡 可能的解决方案:`);
        console.error(`   1. 检查代理服务器是否正常运行`);
        console.error(`   2. 验证代理认证信息是否正确`);
        console.error(`   3. 确认网络连接是否正常`);
        console.error(`   4. 检查防火墙设置`);
        console.error(`   5. 尝试设置 DISABLE_TLS_VERIFY=true 环境变量`);
        process.exit(1);
    }
}

// 运行测试
if (require.main === module) {
    runTest().catch(console.error);
}

module.exports = { parseProxyConfig, testConnection };

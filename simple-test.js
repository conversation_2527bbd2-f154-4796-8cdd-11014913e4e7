#!/usr/bin/env node

/**
 * 简单的网络连接测试
 */

const https = require('https');

function testConnection(hostname, path = '/') {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: hostname,
            port: 443,
            path: path,
            method: 'HEAD',
            timeout: 10000,
            headers: {
                'User-Agent': 'Z2API-Test/1.0'
            }
        };

        console.log(`🔄 测试连接到 ${hostname}${path}`);

        const req = https.request(options, (res) => {
            console.log(`✅ 连接成功！状态码: ${res.statusCode}`);
            resolve({
                status: res.statusCode,
                headers: res.headers
            });
        });

        req.on('error', (error) => {
            console.error(`❌ 连接失败: ${error.message}`);
            reject(error);
        });

        req.on('timeout', () => {
            console.error(`❌ 连接超时`);
            req.destroy();
            reject(new Error('Connection timeout'));
        });

        req.end();
    });
}

async function runTests() {
    console.log('🚀 开始网络连接测试...\n');

    const testTargets = [
        { hostname: 'www.google.com', path: '/' },
        { hostname: 'api.openai.com', path: '/v1/models' },
        { hostname: 'chat.z.ai', path: '/api/chat/completions' }
    ];

    for (const target of testTargets) {
        try {
            await testConnection(target.hostname, target.path);
            console.log('');
        } catch (error) {
            console.log(`跳过 ${target.hostname}，继续下一个测试...\n`);
        }
    }

    console.log('🎉 测试完成！');
}

if (require.main === module) {
    runTests().catch(console.error);
}

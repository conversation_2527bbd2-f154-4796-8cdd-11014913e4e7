# Z2API Node.js 版本

这是基于原Python项目完整功能实现的Node.js单文件版本的Z.AI代理服务。

## 🚀 快速开始

### 环境要求

- Node.js 16.0.0+
- npm

### 安装步骤

1. **安装依赖**

```bash
npm install express axios cors dotenv uuid
```

2. **配置环境变量**

创建 `.env` 文件：

```bash
# 复制示例配置
cp .env.example .env
# 编辑 .env 文件，配置你的参数
```

3. **启动服务器**

```bash
node z2api.js
```

服务器将在 `http://localhost:8000` 启动

## ⚙️ 配置说明

在 `.env` 文件中配置以下参数：

| 参数              | 描述            | 默认值                 | 必需 |
| ----------------- | --------------- |---------------------| ---- |
| `HOST`            | 服务器监听地址  | `0.0.0.0`           | 否   |
| `PORT`            | 服务器端口      | `8000`              | 否   |
| `API_KEY`         | 外部认证密钥    | `sk-z2api-key-2024` | 否   |
| `SHOW_THINK_TAGS` | 显示思考内容    | `false`             | 否   |
| `DEFAULT_STREAM`  | 默认流式模式    | `false`             | 否   |
| `Z_AI_COOKIES`    | Z.AI JWT tokens | -                   | 是   |
| `LOG_LEVEL`       | 日志级别        | `INFO`              | 否   |

## 🔧 使用示例

### 使用 curl 测试

```bash
# 列出模型
curl http://localhost:8000/v1/models

# 聊天完成（非流式）
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-z2api-key-2024" \
  -d '{
    "model": "GLM-4.5",
    "messages": [
      {"role": "user", "content": "Hello!"}
    ]
  }'

# 聊天完成（流式）
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-z2api-key-2024" \
  -d '{
    "model": "GLM-4.5",
    "messages": [
      {"role": "user", "content": "Hello!"}
    ],
    "stream": true
  }'

# 健康检查
curl http://localhost:8000/health
```

### 使用 OpenAI SDK

```javascript
const OpenAI = require('openai');

const client = new OpenAI({
  baseURL: 'http://localhost:8000/v1',
  apiKey: 'sk-z2api-key-2024'
});

async function main() {
  const completion = await client.chat.completions.create({
    messages: [{ role: 'user', content: 'Hello!' }],
    model: 'GLM-4.5',
  });

  console.log(completion.choices[0].message.content);
}

main();
```

## 📋 API 端点

- `GET /v1/models` - 列出可用模型
- `POST /v1/chat/completions` - 聊天完成接口
- `GET /health` - 健康检查

## ✨ 功能特性

- 🔌 **OpenAI SDK 完全兼容** - 无缝替换 OpenAI API
- 🍪 **智能 Cookie 池管理** - 多 token 轮换，自动故障转移
- 🧠 **智能内容过滤** - 非流式响应可选择隐藏 AI 思考过程
- 🌊 **灵活响应模式** - 支持流式和非流式响应，可配置默认模式
- 🛡️ **安全认证** - 固定 API Key 验证
- 📊 **健康检查** - 自动监控和恢复
- 📝 **详细日志** - 完善的调试和监控信息

## 🛠️ 开发模式

使用 Node.js 的 watch 模式进行开发：

```bash
node --watch z2api.js
```

## 🐳 Docker 部署

创建 Dockerfile：

```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package.json .
RUN npm install

COPY z2api.js .
COPY .env .

EXPOSE 8000

CMD ["node", "z2api.js"]
```

构建和运行：

```bash
docker build -t z2api-nodejs .
docker run -d -p 8000:8000 --env-file .env z2api-nodejs
```

## 📝 注意事项

1. 确保设置了有效的 `Z_AI_COOKIES` 环境变量
2. Cookie 格式为逗号分隔的 JWT token
3. 服务会自动进行 Cookie 健康检查和故障转移
4. 流式响应和非流式响应都完全兼容 OpenAI API 格式

## 🔍 故障排除

### 常见问题

1. **No Z.AI cookies configured**
   - 检查 `Z_AI_COOKIES` 环境变量是否正确设置

2. **Invalid authentication**
   - 检查 Cookie 是否有效和未过期

3. **Model not supported**
   - 确保使用 `GLM-4.5` 模型名称

### 调试模式

启用详细日志：

```bash
LOG_LEVEL=DEBUG node z2api.js
```

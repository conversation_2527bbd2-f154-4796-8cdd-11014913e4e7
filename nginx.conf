events {
    worker_connections 1024;
}

http {
    upstream z2api {
        server z2api:8000;
    }

    # 限制请求大小
    client_max_body_size 10M;

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    # 基本HTTP服务器配置
    server {
        listen 80;
        server_name localhost;
        
        access_log /var/log/nginx/access.log main;
        error_log /var/log/nginx/error.log;

        # 健康检查端点
        location /health {
            proxy_pass http://z2api/health;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # API端点
        location / {
            proxy_pass http://z2api;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 支持流式响应
            proxy_buffering off;
            proxy_cache off;
            
            # 超时设置
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }
    }

    # HTTPS服务器配置（可选）
    # server {
    #     listen 443 ssl http2;
    #     server_name localhost;
    #     
    #     ssl_certificate /etc/nginx/ssl/cert.pem;
    #     ssl_certificate_key /etc/nginx/ssl/key.pem;
    #     
    #     ssl_protocols TLSv1.2 TLSv1.3;
    #     ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    #     ssl_prefer_server_ciphers off;
    #     
    #     access_log /var/log/nginx/access.log main;
    #     error_log /var/log/nginx/error.log;
    #     
    #     location / {
    #         proxy_pass http://z2api;
    #         proxy_set_header Host $host;
    #         proxy_set_header X-Real-IP $remote_addr;
    #         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #         proxy_set_header X-Forwarded-Proto $scheme;
    #         
    #         proxy_buffering off;
    #         proxy_cache off;
    #         
    #         proxy_connect_timeout 60s;
    #         proxy_send_timeout 60s;
    #         proxy_read_timeout 60s;
    #     }
    # }
}
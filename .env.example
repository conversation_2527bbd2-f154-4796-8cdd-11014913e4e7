# Z2API Configuration Example

# Environment file loading control
# 环境文件加载控制
# Set to 'true' or '1' to disable .env file loading
# 设置为 'true' 或 '1' 来禁用.env文件加载
# DISABLE_DOTENV=false

# 服务器设置
HOST=0.0.0.0
PORT=8000

# API Key for external authentication (OpenAI SDK compatible)
# 外部认证密钥 (与OpenAI SDK兼容)
API_KEY=sk-z2api-key-2024

# Content filtering settings (only applies to non-streaming responses)
# 内容过滤设置 (仅适用于非流式响应)
# Whether to show <think> tags in response (true/false)
# 是否在响应中显示思考标签 (true/false)
SHOW_THINK_TAGS=false

# Response mode settings
# 响应模式设置
# Default streaming mode (true/false)
# 默认流式模式 (true/false)
DEFAULT_STREAM=false

# Streaming timeout settings
# 流式响应超时设置
# Timeout in seconds for streaming data reception (default: 30)
# 流式数据接收超时时间，单位秒 (默认: 30)
STREAM_TIMEOUT=40

# Z.AI Tokens (comma-separated list of JWT tokens from z.ai)
# Z.AI令牌 (从z.ai获取的JWT令牌，逗号分隔)
# Get these from https://chat.z.ai by inspecting network requests
# 从 https://chat.z.ai 的网络请求中获取这些令牌
Z_AI_COOKIES=your_jwt_token_here

# Rate limiting
# 速率限制
MAX_REQUESTS_PER_MINUTE=60

# Logging level (DEBUG, INFO, WARNING, ERROR)
# 日志级别 (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# Auto refresh settings (currently not implemented)
# 自动刷新设置 (当前未实现)
AUTO_REFRESH_TOKENS=false
REFRESH_CHECK_INTERVAL=3600

# HTTP Proxy settings (optional)
# HTTP代理设置 (可选)
# Format: http://[username:password@]host:port
# 格式: http://[用户名:密码@]主机:端口
# HTTP_PROXY=http://proxy.example.com:8080
# HTTP_PROXY=http://username:<EMAIL>:8080
# HTTPS_PROXY=http://proxy.example.com:8080

# TLS Certificate Verification (optional)
# TLS证书验证 (可选)
# Set to 'false' or '0' to disable TLS certificate verification
# 设置为 'false' 或 '0' 来禁用TLS证书验证
# DISABLE_TLS_VERIFY=false
# NODE_TLS_REJECT_UNAUTHORIZED=0

# Z2API 代理优化说明

## 优化内容

本次优化主要解决了 "Client network socket disconnected before secure TLS connection was established" 错误，并提升了代理连接的稳定性和可靠性。

## 主要改进

### 1. 增强的错误处理
- 添加了详细的错误分类和处理
- 改进了 Socket 错误监听和处理
- 增加了连接状态监控

### 2. 优化的超时机制
- 分离了连接超时和请求超时
- 支持通过环境变量配置超时时间
- 为流式请求设置了更长的超时时间

### 3. 改进的 Socket 管理
- 启用了 Keep-Alive 和 NoDelay 选项
- 添加了 Socket 错误和关闭事件监听
- 改进了 Socket 生命周期管理

### 4. 增强的代理支持
- 添加了标准的 HTTP 头部支持
- 改进了代理认证处理
- 增加了 SNI (Server Name Indication) 支持

### 5. 重试机制
- 实现了智能重试逻辑
- 支持指数退避策略
- 可配置的重试次数和延迟

## 新增环境变量

```bash
# 代理连接超时（秒）
PROXY_CONNECT_TIMEOUT=30

# 代理请求超时（秒）
PROXY_REQUEST_TIMEOUT=600

# 最大重试次数
PROXY_MAX_RETRIES=3

# 重试延迟（毫秒）
PROXY_RETRY_DELAY=1000

# 禁用 TLS 验证（如果代理有证书问题）
DISABLE_TLS_VERIFY=true
# 或者
NODE_TLS_REJECT_UNAUTHORIZED=0
```

## 使用示例

### 基本代理配置
```bash
# HTTP 代理
export HTTP_PROXY=http://proxy.example.com:8080

# HTTPS 代理
export HTTPS_PROXY=https://proxy.example.com:8080

# 带认证的代理
export HTTP_PROXY=http://username:<EMAIL>:8080
```

### 高级配置
```bash
# 完整配置示例
export HTTP_PROXY=http://username:<EMAIL>:8080
export PROXY_CONNECT_TIMEOUT=30
export PROXY_REQUEST_TIMEOUT=600
export PROXY_MAX_RETRIES=3
export PROXY_RETRY_DELAY=1000
export DISABLE_TLS_VERIFY=true
```

## 测试代理配置

使用提供的测试脚本验证代理配置：

```bash
# 测试代理连接
node test-proxy.js
```

测试脚本会：
1. 解析代理配置
2. 尝试建立代理连接
3. 测试 HTTPS 隧道
4. 验证与目标服务器的连接

## 故障排除

### 常见错误及解决方案

#### 1. "Client network socket disconnected before secure TLS connection was established"
**原因**: Socket 在 TLS 握手完成前断开连接
**解决方案**:
- 增加连接超时时间：`PROXY_CONNECT_TIMEOUT=60`
- 启用重试机制：`PROXY_MAX_RETRIES=5`
- 检查代理服务器状态

#### 2. "Proxy connection timeout"
**原因**: 代理服务器响应缓慢或不可达
**解决方案**:
- 检查代理服务器地址和端口
- 增加连接超时：`PROXY_CONNECT_TIMEOUT=60`
- 验证网络连接

#### 3. "Proxy connection failed: 407"
**原因**: 代理认证失败
**解决方案**:
- 检查用户名和密码
- 确认代理 URL 格式：`**********************:port`

#### 4. "HTTPS request timeout through proxy"
**原因**: 通过代理的 HTTPS 请求超时
**解决方案**:
- 增加请求超时：`PROXY_REQUEST_TIMEOUT=1200`
- 检查目标服务器可达性

#### 5. TLS 证书错误
**原因**: 代理或目标服务器的 TLS 证书问题
**解决方案**:
- 临时禁用 TLS 验证：`DISABLE_TLS_VERIFY=true`
- 更新系统证书库

## 性能优化建议

### 1. 连接池
- 启用 Keep-Alive 连接复用
- 设置合适的连接超时时间

### 2. 重试策略
- 根据网络环境调整重试次数
- 使用指数退避避免过度重试

### 3. 超时设置
- 连接超时：30-60秒
- 请求超时：5-10分钟（流式请求）
- 根据实际网络情况调整

### 4. 监控和日志
- 启用详细日志：`LOG_LEVEL=DEBUG`
- 监控连接成功率和响应时间
- 定期检查代理服务器状态

## 兼容性

- Node.js 14+
- 支持 HTTP/HTTPS 代理
- 兼容 SOCKS 代理（通过 HTTP 代理转换）
- 支持代理认证（Basic Auth）

## 安全注意事项

1. **认证信息保护**: 不要在日志中记录代理密码
2. **TLS 验证**: 生产环境中避免禁用 TLS 验证
3. **网络隔离**: 确保代理服务器的网络安全
4. **访问控制**: 限制代理服务器的访问权限

## 更新日志

### v1.1.0
- 修复 TLS 连接断开问题
- 添加重试机制
- 改进错误处理
- 增强 Socket 管理
- 添加配置选项
- 提供测试工具
